// 全局样式文件
// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// 全局变量
:root {
  // 主题色彩
  --primary-color: #0052D9;
  --primary-light: #366ef4;
  --primary-dark: #003cab;
  
  --secondary-color: #FF8A00;
  --secondary-light: #FFB366;
  --secondary-dark: #E37318;
  
  // 中性色彩
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-placeholder: #999999;
  --text-disabled: #CCCCCC;
  
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #F5F5F5;
  
  --border-color: #E8E8E8;
  --divider-color: #F0F0F0;
  
  // 阴影
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.15);
  
  // 圆角
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-xl: 20px;
  --radius-round: 50%;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
}

// 全局字体设置
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 渐变背景工具类
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
}

.gradient-cool {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-warm {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

// 卡片样式
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

.card-elevated {
  box-shadow: var(--shadow-medium);
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--radius-xl);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  
  &:hover {
    background: var(--primary-light);
  }
}

.btn-secondary {
  background: var(--secondary-color);
  color: white;
  
  &:hover {
    background: var(--secondary-light);
  }
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  
  &:hover {
    background: var(--primary-color);
    color: white;
  }
}

// 输入框样式
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: 16px;
  background: var(--bg-primary);
  transition: border-color 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
  
  &::placeholder {
    color: var(--text-placeholder);
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 动画工具类
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

// 响应式断点
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

// 安全区域适配
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}